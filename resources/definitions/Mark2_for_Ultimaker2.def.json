{"version": 2, "name": "Mark2 for Ultimaker2", "inherits": "ultimaker2_plus", "metadata": {"visible": true, "author": "TheUltimakerCommunity", "manufacturer": "Foehnsturm", "file_formats": "text/x-gcode", "platform": "ultimaker2_platform.obj", "has_machine_quality": false, "has_materials": true, "has_variants": true, "icon": "icon_ultimaker.png", "machine_extruder_trains": {"0": "Mark2_extruder1", "1": "Mark2_extruder2"}, "platform_texture": "Mark2_for_Ultimaker2_backplate.png", "supported_actions": ["MachineSettingsAction", "UpgradeFirmware"], "weight": 0}, "overrides": {"acceleration_enabled": {"default_value": true}, "acceleration_layer_0": {"value": "acceleration_topbottom"}, "acceleration_prime_tower": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_print": {"value": "2000"}, "acceleration_support": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_support_interface": {"value": "acceleration_topbottom"}, "acceleration_topbottom": {"value": "math.ceil(acceleration_print * 500 / 4000)"}, "acceleration_travel": {"value": "acceleration_print if magic_spiralize else 3000"}, "acceleration_wall": {"value": "math.ceil(acceleration_print * 1000 / 4000)"}, "acceleration_wall_0": {"value": "math.ceil(acceleration_wall * 500 / 1000)"}, "extruder_prime_pos_abs": {"default_value": false}, "extruder_prime_pos_x": {"default_value": 0.0, "enabled": false}, "extruder_prime_pos_y": {"default_value": 0.0, "enabled": false}, "extruder_prime_pos_z": {"default_value": 0.0, "enabled": false}, "gantry_height": {"value": "52"}, "jerk_enabled": {"default_value": true}, "jerk_layer_0": {"value": "jerk_topbottom"}, "jerk_prime_tower": {"value": "10 if jerk_print < 16 else math.ceil(jerk_print * 15 / 25)"}, "jerk_support": {"value": "10 if jerk_print < 16 else math.ceil(jerk_print * 15 / 25)"}, "jerk_support_interface": {"value": "jerk_topbottom"}, "jerk_topbottom": {"value": "10 if jerk_print < 25 else math.ceil(jerk_print * 10 / 25)"}, "jerk_travel": {"value": "jerk_print if magic_spiralize else 20"}, "jerk_travel_layer_0": {"value": "math.ceil(jerk_layer_0 * jerk_travel / jerk_print)"}, "jerk_wall": {"value": "10 if jerk_print < 16 else math.ceil(jerk_print * 15 / 25)"}, "jerk_wall_0": {"value": "10 if jerk_wall < 16 else math.ceil(jerk_wall * 6 / 10)"}, "layer_height_0": {"value": "round(machine_nozzle_size / 1.5, 2)"}, "layer_start_x": {"default_value": 105.0, "enabled": false}, "layer_start_y": {"default_value": 27.0, "enabled": false}, "line_width": {"value": "round(machine_nozzle_size * 0.875, 2)"}, "machine_acceleration": {"default_value": 3000}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 223}, "machine_disallowed_areas": {"default_value": [[[-115, 112.5], [-10, 112.5], [-10, 72.5], [-115, 72.5]], [[115, 112.5], [115, 72.5], [15, 72.5], [15, 112.5]], [[-115, -112.5], [-115, -87.5], [115, -87.5], [115, -112.5]], [[-115, 72.5], [-97, 72.5], [-97, -112.5], [-115, -112.5]]]}, "machine_end_gcode": {"value": "\"\"  if machine_gcode_flavor == \"UltiGCode\" else \"G90 ;absolute positioning\\nM104 S0 T0 ;extruder heater off\\nM104 S0 T1\\nM140 S0 ;turn off bed\\nT0 ; move to the first head\\nM107 ;fan off\""}, "machine_extruder_count": {"default_value": 2}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-44, 14], [-44, -34], [64, 14], [64, -34]]}, "machine_heat_zone_length": {"default_value": 20}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 203}, "machine_max_feedrate_e": {"default_value": 45}, "machine_max_feedrate_x": {"default_value": 250}, "machine_max_feedrate_y": {"default_value": 250}, "machine_max_feedrate_z": {"default_value": 40}, "machine_min_cool_heat_time_window": {"default_value": 15.0}, "machine_name": {"default_value": "Mark2_for_Ultimaker2"}, "machine_nozzle_cool_down_speed": {"default_value": 1.5}, "machine_nozzle_head_distance": {"default_value": 5}, "machine_nozzle_heat_up_speed": {"default_value": 3.5}, "machine_nozzle_size": {"default_value": 0.4}, "machine_show_variants": {"default_value": true}, "machine_start_gcode": {"value": "\"\"  if machine_gcode_flavor == \"UltiGCode\" else \"G21 ;metric values\\nG90 ;absolute positioning\\nM82 ;set extruder to absolute mode\\nM107 ;start with the fan off\\nM200 D0 T0 ;reset filament diameter\\nM200 D0 T1\\nG28 Z0; home all\\nG28 X0 Y0\\nG0 Z20 F2400 ;move the platform to 20mm\\nG92 E0\\nM190 S{material_bed_temperature_layer_0}\\nM109 T0 S{material_standby_temperature, 0}\\nM109 T1 S{material_print_temperature_layer_0, 1}\\nM104 T0 S{material_print_temperature_layer_0, 0}\\nT1 ; move to the 2th head\\nG0 Z20 F2400\\nG92 E-7.0 ;prime distance\\nG1 E0 F45 ;purge nozzle\\nG1 E-5.1 F1500 ; retract\\nG1 X90 Z0.01 F5000 ; move away from the prime poop\\nG1 X50 F9000\\nG0 Z20 F2400\\nT0 ; move to the first head\\nM104 T1 S{material_standby_temperature, 1}\\nG0 Z20 F2400\\nM104 T{initial_extruder_nr} S{material_print_temperature_layer_0, initial_extruder_nr}\\nG92 E-7.0\\nG1 E0 F45 ;purge nozzle\\nG1 X60 Z0.01 F5000 ; move away from the prime poop\\nG1 X20 F9000\\nM400 ;finish all moves\\nG92 E0\\n;end of startup sequence\\n\""}, "machine_use_extruder_offset_to_offset_coords": {"default_value": false}, "machine_width": {"default_value": 223}, "prime_tower_position_y": {"value": "170"}, "retraction_amount": {"default_value": 5.1}, "retraction_speed": {"default_value": 25}, "switch_extruder_prime_speed": {"enabled": false, "value": "retraction_prime_speed"}, "switch_extruder_retraction_amount": {"enabled": false, "value": "retraction_amount"}, "switch_extruder_retraction_speed": {"enabled": false, "value": "retraction_retract_speed"}, "switch_extruder_retraction_speeds": {"enabled": false, "value": "retraction_speed"}}}