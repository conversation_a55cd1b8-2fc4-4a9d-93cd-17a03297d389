{"version": 2, "name": "Ultimaker 2+", "inherits": "ultimaker2", "metadata": {"author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "text/x-gcode", "platform": "ultimaker2_platform.obj", "exclude_materials": ["generic_hips", "generic_petg", "generic_bam", "ultimaker_bam", "generic_pva", "ultimaker_pva", "generic_tough_pla", "ultimaker_tough_pla_black", "ultimaker_tough_pla_green", "ultimaker_tough_pla_red", "ultimaker_tough_pla_white", "ultimaker_tough_pla_blue", "ultimaker_tough_pla_gray", "ultimaker_tough_pla_yellow", "generic_cffcpe", "generic_cffpa", "generic_gffcpe", "generic_gffpa", "generic_petcf", "structur3d_dap100silicone", "ultimaker_petg_red", "ultimaker_petg_blue", "ultimaker_petg_grey", "ultimaker_petg_black", "ultimaker_petg_green", "ultimaker_petg_white", "ultimaker_petg_orange", "ultimaker_petg_silver", "ultimaker_petg_yellow", "ultimaker_petg_transparent", "ultimaker_petg_red_translucent", "ultimaker_petg_blue_translucent", "ultimaker_petg_green_translucent", "ultimaker_petg_yellow_fluorescent", "ultimaker_petcf_black", "ultimaker_petcf_blue", "ultimaker_petcf_gray"], "firmware_file": "MarlinUltimaker2plus.hex", "first_start_actions": [], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker2_plus_extruder_0"}, "platform_texture": "Ultimaker2Plusbackplate.png", "preferred_variant_name": "0.4 mm", "supported_actions": [], "weight": 1}, "overrides": {"gantry_height": {"value": "52"}, "layer_height_0": {"value": "round(machine_nozzle_size / 1.5, 2)"}, "machine_disallowed_areas": {"default_value": [[[-115, 112.5], [-78, 112.5], [-80, 102.5], [-115, 102.5]], [[115, 112.5], [115, 102.5], [105, 102.5], [103, 112.5]], [[-115, -112.5], [-115, -104.5], [-84, -104.5], [-82, -112.5]], [[115, -112.5], [108, -112.5], [110, -104.5], [115, -104.5]]]}, "machine_head_with_fans_polygon": {"default_value": [[-44, 14], [-44, -34], [64, 14], [64, -34]]}, "machine_heat_zone_length": {"default_value": 20}, "machine_height": {"default_value": 205}, "machine_name": {"default_value": "Ultimaker 2+"}, "machine_nozzle_head_distance": {"default_value": 5}, "machine_show_variants": {"default_value": true}, "speed_infill": {"value": "speed_print"}, "speed_support": {"value": "speed_wall_0"}, "speed_wall_x": {"value": "speed_wall"}}}