{"version": 2, "name": "Atom 3", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "Layer One", "platform": "Atom 3 bed.3mf", "has_machine_quality": false, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "atom3_extruder_0"}, "platform_offset": [0, 0, 0], "preferred_material": "layer_one_dark_gray_pla", "preferred_quality_type": "normal", "preferred_variant_name": "PTFE hotend + 0.4mm brass nozzle", "variants_name": "Tool:"}, "overrides": {"adhesion_type": {"default_value": "skirt"}, "cool_lift_head": {"default_value": false}, "cool_min_layer_time": {"default_value": 5}, "cool_min_speed": {"default_value": 10}, "default_material_bed_temperature": {"default_value": 60, "maximum_value": "120", "maximum_value_warning": "115", "minimum_value": "0", "minimum_value_warning": "build_volume_temperature"}, "default_material_print_temperature": {"default_value": 200}, "infill_before_walls": {"value": false}, "infill_sparse_density": {"default_value": 17}, "initial_layer_line_width_factor": {"default_value": 100}, "layer_height": {"default_value": 0.2}, "layer_height_0": {"default_value": 0.2, "value": "layer_height"}, "line_width": {"value": "machine_nozzle_size"}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 270}, "machine_end_gcode": {"default_value": ";MACHINE END CODE\nG91 ;relative positioning\nG1 E-1 F300  ;retract  filament release pressure\nG1 Z+1.0 E-5 F9000 ;move up a and retract more\nG90 ;absolute positioning\nG28; home\nM84 ;steppers off\n;MACHINE END CODE"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-23.82, 51.25], [23.82, 51.25], [56.292, -5.0], [32.476, -46.25], [-32.476, -46.25], [-56.292, -5.0]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 340}, "machine_name": {"default_value": "Atom 3"}, "machine_nozzle_head_distance": {"default_value": 6}, "machine_shape": {"default_value": "elliptic"}, "machine_show_variants": {"default_value": true}, "machine_start_gcode": {"default_value": ";MACHINE START CODE\nG21 ;metric values\nG90 ;absolute positioning\nG28 ;home\nG1 Z5 F9000\n;MACHINE START CODE"}, "machine_width": {"default_value": 270}, "material_bed_temperature": {"maximum_value": "120", "maximum_value_warning": "115", "minimum_value": "0", "minimum_value_warning": "build_volume_temperature", "value": "round(default_material_bed_temperature-(-0.202 * default_material_bed_temperature + 7.16)) if default_material_bed_temperature > 40 else default_material_bed_temperature"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature_layer_0"}, "retraction_amount": {"default_value": 7, "maximum_value_warning": 9}, "retraction_hop": {"default_value": 0.5}, "retraction_hop_enabled": {"default_value": true}, "retraction_speed": {"default_value": 70, "maximum_value_warning": 80}, "skirt_brim_minimal_length": {"default_value": 750, "maximum_value_warning": "4000", "minimum_value": "0", "minimum_value_warning": "25", "value": "60 / (layer_height_0 * line_width)"}, "skirt_gap": {"default_value": 1, "value": "3 * wall_line_width_0"}, "speed_layer_0": {"value": "20"}, "speed_print": {"default_value": 40}, "speed_slowdown_layers": {"default_value": 1}, "speed_wall": {"value": "speed_print * 0.75"}, "speed_wall_0": {"value": "speed_print * 0.5"}, "speed_wall_x": {"value": "speed_print * 0.75"}, "support_angle": {"default_value": 60}, "support_type": {"default_value": "everywhere"}, "support_xy_distance_overhang": {"value": "machine_nozzle_size"}, "support_z_distance": {"value": "layer_height"}, "top_bottom_thickness": {"default_value": 1.0}, "zig_zaggify_infill": {"value": true}}}