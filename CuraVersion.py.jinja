# Copyright (c) 2023 UltiMaker
# Cura is released under the terms of the LGPLv3 or higher.

CuraAppName = "{{ cura_app_name }}"
CuraAppDisplayName = "{{ cura_app_display_name  }}"
CuraVersion = "{{ cura_version }}"
CuraBuildType = "{{ cura_build_type }}"
CuraDebugMode = {{ cura_debug_mode }}
CuraCloudAPIRoot = "{{ cura_cloud_api_root }}"
CuraCloudAPIVersion = "{{ cura_cloud_api_version }}"
CuraCloudAccountAPIRoot = "{{ cura_cloud_account_api_root }}"
CuraMarketplaceRoot = "{{ cura_marketplace_root }}"
CuraDigitalFactoryURL = "{{ cura_digital_factory_url }}"
CuraLatestURL = "{{ cura_latest_url }}"

ConanInstalls = {{ conan_installs }}
PythonInstalls = {{ python_installs }}
